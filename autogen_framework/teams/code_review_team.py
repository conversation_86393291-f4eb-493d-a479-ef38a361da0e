"""
代码审查团队实现

创建一个专门进行代码审查的团队，包含高级代码审查者和资深开发者。
团队专注于代码质量、安全性、性能和可维护性的全面审查。
"""

from typing import Dict, Any, Optional, List

try:
    from autogen_agentchat.teams import RoundRobinGroupChat
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.conditions import MaxMessageTermination
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果AutoGen不可用，使用模拟类
    class RoundRobinGroupChat:
        def __init__(self, participants, **kwargs):
            self.participants = participants
            self.termination_condition = kwargs.get('termination_condition')
            self.max_turns = kwargs.get('max_turns')

    class MaxMessageTermination:
        def __init__(self, max_messages):
            self.max_messages = max_messages

    AssistantAgent = None
    AUTOGEN_AVAILABLE = False

from managers.agent_manager import AgentManager
from managers.model_manager import ModelManager


def create_code_review_team(
    model_alias: str,
    agent_manager: Agent<PERSON><PERSON>ger,
    model_manager: Optional[ModelManager] = None,
    max_turns: int = 8,
    **kwargs
) -> RoundRobinGroupChat:
    """
    创建代码审查团队
    
    Args:
        model_alias: 模型别名
        agent_manager: Agent管理器实例
        model_manager: 模型管理器实例
        max_round: 最大对话轮数
        speaker_selection_method: 发言者选择方法
        **kwargs: 其他配置参数
    
    Returns:
        GroupChatManager实例
    """
    if model_manager is None:
        model_manager = ModelManager()
    
    # 创建团队中的agents
    agents = []
    
    # 高级代码审查者Agent
    try:
        senior_reviewer_config = kwargs.get('senior_reviewer_config', {})
        senior_reviewer_config.setdefault('name', "SeniorCodeReviewer")
        senior_reviewer = agent_manager.create_agent(
            "senior_code_reviewer",
            model_alias,
            **senior_reviewer_config
        )
        agents.append(senior_reviewer)
    except Exception as e:
        print(f"Warning: Could not create senior_code_reviewer agent: {e}")

    # 普通代码审查者Agent
    try:
        reviewer_config = kwargs.get('reviewer_config', {})
        reviewer_config.setdefault('name', "CodeReviewer")
        code_reviewer = agent_manager.create_agent(
            "code_reviewer",
            model_alias,
            **reviewer_config
        )
        agents.append(code_reviewer)
    except Exception as e:
        print(f"Warning: Could not create code_reviewer agent: {e}")
    
    # 如果没有可用的agents，创建模拟agents
    if not agents and not AUTOGEN_AVAILABLE:
        class MockAgent:
            def __init__(self, name):
                self.name = name
        
        agents = [
            MockAgent("SeniorCodeReviewer"),
            MockAgent("CodeReviewer")
        ]
    
    if not agents:
        raise ValueError("No agents available for the code review team")
    
    # 创建终止条件
    termination_condition = MaxMessageTermination(max_turns)

    # 创建RoundRobinGroupChat团队
    team = RoundRobinGroupChat(
        participants=agents,
        termination_condition=termination_condition,
        max_turns=max_turns,
        **kwargs.get('team_config', {})
    )

    return team


def create_security_review_team(
    model_alias: str,
    agent_manager: AgentManager,
    model_manager: Optional[ModelManager] = None,
    **kwargs
) -> RoundRobinGroupChat:
    """
    创建安全审查团队（专注于安全性审查）
    
    Args:
        model_alias: 模型别名
        agent_manager: Agent管理器实例
        model_manager: 模型管理器实例
        **kwargs: 其他配置参数
    
    Returns:
        RoundRobinGroupChat实例
    """
    if model_manager is None:
        model_manager = ModelManager()
    
    # 创建团队中的agents
    agents = []
    
    # 高级代码审查者Agent（专注安全）
    try:
        security_expert_config = kwargs.get('security_expert_config', {})
        security_expert_config.setdefault('name', "SecurityExpert")
        security_expert_config.setdefault('special_instructions', "专注于安全漏洞检测、权限控制审查和数据安全分析")
        senior_reviewer = agent_manager.create_agent(
            "senior_code_reviewer",
            model_alias,
            **security_expert_config
        )
        agents.append(senior_reviewer)
    except Exception as e:
        print(f"Warning: Could not create security expert agent: {e}")

    # 代码审查者Agent（专注代码质量）
    try:
        quality_analyst_config = kwargs.get('quality_analyst_config', {})
        quality_analyst_config.setdefault('name', "QualityAnalyst")
        quality_analyst_config.setdefault('special_instructions', "专注于代码质量、最佳实践和潜在风险识别")
        code_reviewer = agent_manager.create_agent(
            "code_reviewer",
            model_alias,
            **quality_analyst_config
        )
        agents.append(code_reviewer)
    except Exception as e:
        print(f"Warning: Could not create quality analyst agent: {e}")
    
    # 如果没有可用的agents，创建模拟agents
    if not agents and not AUTOGEN_AVAILABLE:
        class MockAgent:
            def __init__(self, name):
                self.name = name
        
        agents = [
            MockAgent("SecurityExpert"),
            MockAgent("QualityAnalyst")
        ]
    
    if not agents:
        raise ValueError("No agents available for the security review team")
    
    # 创建终止条件
    max_turns = kwargs.get('max_turns', 6)
    termination_condition = MaxMessageTermination(max_turns)

    # 创建RoundRobinGroupChat团队
    team = RoundRobinGroupChat(
        participants=agents,
        termination_condition=termination_condition,
        max_turns=max_turns,
        **kwargs.get('team_config', {})
    )

    return team
