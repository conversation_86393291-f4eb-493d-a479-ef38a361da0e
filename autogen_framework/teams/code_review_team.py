"""
代码审查团队实现

创建一个专门进行代码审查的团队，包含高级代码审查者和资深开发者。
团队专注于代码质量、安全性、性能和可维护性的全面审查。
"""

from typing import Dict, Any, Optional, List

try:
    from autogen_agentchat.teams import GroupChat, GroupChatManager
    from autogen_agentchat.agents import AssistantAgent
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果AutoGen不可用，使用模拟类
    class GroupChat:
        def __init__(self, **kwargs):
            self.agents = kwargs.get('agents', [])
            self.messages = kwargs.get('messages', [])
            self.max_round = kwargs.get('max_round', 10)
    
    class GroupChatManager:
        def __init__(self, **kwargs):
            self.groupchat = kwargs.get('groupchat')
            self.llm_config = kwargs.get('llm_config')
    
    AssistantAgent = None
    AUTOGEN_AVAILABLE = False

from managers.agent_manager import <PERSON><PERSON>ana<PERSON>
from managers.model_manager import <PERSON><PERSON><PERSON>ger


def create_code_review_team(
    model_alias: str,
    agent_manager: Agent<PERSON><PERSON><PERSON>,
    model_manager: Optional[ModelManager] = None,
    max_round: int = 8,
    speaker_selection_method: str = "round_robin",
    **kwargs
) -> GroupChatManager:
    """
    创建代码审查团队
    
    Args:
        model_alias: 模型别名
        agent_manager: Agent管理器实例
        model_manager: 模型管理器实例
        max_round: 最大对话轮数
        speaker_selection_method: 发言者选择方法
        **kwargs: 其他配置参数
    
    Returns:
        GroupChatManager实例
    """
    if model_manager is None:
        model_manager = ModelManager()
    
    # 创建团队中的agents
    agents = []
    
    # 高级代码审查者Agent
    try:
        senior_reviewer = agent_manager.create_agent(
            "senior_code_reviewer", 
            model_alias,
            name="SeniorCodeReviewer",
            **kwargs.get('senior_reviewer_config', {})
        )
        agents.append(senior_reviewer)
    except Exception as e:
        print(f"Warning: Could not create senior_code_reviewer agent: {e}")
    
    # 普通代码审查者Agent
    try:
        code_reviewer = agent_manager.create_agent(
            "code_reviewer", 
            model_alias,
            name="CodeReviewer",
            **kwargs.get('reviewer_config', {})
        )
        agents.append(code_reviewer)
    except Exception as e:
        print(f"Warning: Could not create code_reviewer agent: {e}")
    
    # 如果没有可用的agents，创建模拟agents
    if not agents and not AUTOGEN_AVAILABLE:
        class MockAgent:
            def __init__(self, name):
                self.name = name
        
        agents = [
            MockAgent("SeniorCodeReviewer"),
            MockAgent("CodeReviewer")
        ]
    
    if not agents:
        raise ValueError("No agents available for the code review team")
    
    # 创建群聊
    group_chat = GroupChat(
        agents=agents,
        messages=[],
        max_round=max_round,
        speaker_selection_method=speaker_selection_method,
        **kwargs.get('groupchat_config', {})
    )
    
    # 获取模型配置
    try:
        llm_config = model_manager.get_model_config(model_alias)
    except Exception as e:
        print(f"Warning: Could not get model config: {e}")
        llm_config = {"model": "mock_model"}
    
    # 创建管理器
    manager = GroupChatManager(
        groupchat=group_chat,
        llm_config=llm_config,
        name="CodeReviewTeamManager",
        **kwargs.get('manager_config', {})
    )
    
    return manager


def create_security_review_team(
    model_alias: str,
    agent_manager: AgentManager,
    model_manager: Optional[ModelManager] = None,
    **kwargs
) -> GroupChatManager:
    """
    创建安全审查团队（专注于安全性审查）
    
    Args:
        model_alias: 模型别名
        agent_manager: Agent管理器实例
        model_manager: 模型管理器实例
        **kwargs: 其他配置参数
    
    Returns:
        GroupChatManager实例
    """
    if model_manager is None:
        model_manager = ModelManager()
    
    # 创建团队中的agents
    agents = []
    
    # 高级代码审查者Agent（专注安全）
    try:
        senior_reviewer = agent_manager.create_agent(
            "senior_code_reviewer", 
            model_alias,
            name="SecurityExpert",
            special_instructions="专注于安全漏洞检测、权限控制审查和数据安全分析",
            **kwargs.get('security_expert_config', {})
        )
        agents.append(senior_reviewer)
    except Exception as e:
        print(f"Warning: Could not create security expert agent: {e}")
    
    # 代码审查者Agent（专注代码质量）
    try:
        code_reviewer = agent_manager.create_agent(
            "code_reviewer", 
            model_alias,
            name="QualityAnalyst",
            special_instructions="专注于代码质量、最佳实践和潜在风险识别",
            **kwargs.get('quality_analyst_config', {})
        )
        agents.append(code_reviewer)
    except Exception as e:
        print(f"Warning: Could not create quality analyst agent: {e}")
    
    # 如果没有可用的agents，创建模拟agents
    if not agents and not AUTOGEN_AVAILABLE:
        class MockAgent:
            def __init__(self, name):
                self.name = name
        
        agents = [
            MockAgent("SecurityExpert"),
            MockAgent("QualityAnalyst")
        ]
    
    if not agents:
        raise ValueError("No agents available for the security review team")
    
    # 创建群聊
    group_chat = GroupChat(
        agents=agents,
        messages=[],
        max_round=kwargs.get('max_round', 6),
        speaker_selection_method=kwargs.get('speaker_selection_method', "round_robin")
    )
    
    # 获取模型配置
    try:
        llm_config = model_manager.get_model_config(model_alias)
    except Exception as e:
        print(f"Warning: Could not get model config: {e}")
        llm_config = {"model": "mock_model"}
    
    # 创建管理器
    manager = GroupChatManager(
        groupchat=group_chat,
        llm_config=llm_config,
        name="SecurityReviewTeamManager"
    )
    
    return manager
