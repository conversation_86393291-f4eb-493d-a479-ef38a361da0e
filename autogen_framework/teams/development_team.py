"""
开发团队实现

创建一个包含开发者、代码审查者和全栈开发者的开发团队。
团队使用GroupChat进行协作，支持复杂的开发工作流。
"""

from typing import Dict, Any, Optional, List

try:
    from autogen_agentchat.teams import RoundRobinGroupChat
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.conditions import MaxMessageTermination
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果AutoGen不可用，使用模拟类
    class RoundRobinGroupChat:
        def __init__(self, participants, **kwargs):
            self.participants = participants
            self.termination_condition = kwargs.get('termination_condition')
            self.max_turns = kwargs.get('max_turns')

    class MaxMessageTermination:
        def __init__(self, max_messages):
            self.max_messages = max_messages

    AssistantAgent = None
    AUTOGEN_AVAILABLE = False

from managers.agent_manager import AgentManager
from managers.model_manager import ModelManager


def create_development_team(
    model_alias: str,
    agent_manager: Agent<PERSON><PERSON>ger,
    model_manager: Optional[ModelManager] = None,
    max_turns: int = 10,
    **kwargs
) -> RoundRobinGroupChat:
    """
    创建开发团队

    Args:
        model_alias: 模型别名
        agent_manager: Agent管理器实例
        model_manager: 模型管理器实例
        max_turns: 最大对话轮数
        **kwargs: 其他配置参数

    Returns:
        RoundRobinGroupChat实例
    """
    if model_manager is None:
        model_manager = ModelManager()
    
    # 创建团队中的agents
    agents = []
    
    # 开发者Agent
    try:
        developer_config = kwargs.get('developer_config', {})
        # 设置Agent实例名称（这会传递给Agent工厂函数的name参数）
        developer_config.setdefault('name', "Developer")
        developer = agent_manager.create_agent(
            "developer",  # Agent类型名称
            model_alias,  # 模型别名
            **developer_config  # 包含Agent实例名称的配置
        )
        agents.append(developer)
    except Exception as e:
        print(f"Warning: Could not create developer agent: {e}")

    # 代码审查者Agent
    try:
        reviewer_config = kwargs.get('reviewer_config', {})
        reviewer_config.setdefault('name', "CodeReviewer")
        code_reviewer = agent_manager.create_agent(
            "code_reviewer",
            model_alias,
            **reviewer_config
        )
        agents.append(code_reviewer)
    except Exception as e:
        print(f"Warning: Could not create code_reviewer agent: {e}")

    # 全栈开发者Agent
    try:
        fullstack_config = kwargs.get('fullstack_config', {})
        fullstack_config.setdefault('name', "FullstackDeveloper")
        fullstack_developer = agent_manager.create_agent(
            "fullstack_developer",
            model_alias,
            **fullstack_config
        )
        agents.append(fullstack_developer)
    except Exception as e:
        print(f"Warning: Could not create fullstack_developer agent: {e}")
    
    # 如果没有可用的agents，创建一个模拟的agent
    if not agents and not AUTOGEN_AVAILABLE:
        # 创建模拟agent用于测试
        class MockAgent:
            def __init__(self, name):
                self.name = name
        
        agents = [
            MockAgent("Developer"),
            MockAgent("CodeReviewer"),
            MockAgent("FullstackDeveloper")
        ]
    
    if not agents:
        raise ValueError("No agents available for the development team")
    
    # 创建终止条件
    termination_condition = MaxMessageTermination(max_turns)

    # 创建RoundRobinGroupChat团队
    team = RoundRobinGroupChat(
        participants=agents,
        termination_condition=termination_condition,
        max_turns=max_turns,
        **kwargs.get('team_config', {})
    )

    return team


def create_simple_development_team(
    model_alias: str,
    agent_manager: AgentManager,
    **kwargs
) -> RoundRobinGroupChat:
    """
    创建简化的开发团队（只包含开发者和审查者）

    Args:
        model_alias: 模型别名
        agent_manager: Agent管理器实例
        **kwargs: 其他配置参数

    Returns:
        RoundRobinGroupChat实例
    """
    model_manager = ModelManager()
    
    # 创建团队中的agents
    agents = []
    
    # 开发者Agent
    try:
        developer_config = kwargs.get('developer_config', {})
        developer_config.setdefault('name', "Developer")
        developer = agent_manager.create_agent(
            "developer",
            model_alias,
            **developer_config
        )
        agents.append(developer)
    except Exception as e:
        print(f"Warning: Could not create developer agent: {e}")

    # 代码审查者Agent
    try:
        reviewer_config = kwargs.get('reviewer_config', {})
        reviewer_config.setdefault('name', "CodeReviewer")
        code_reviewer = agent_manager.create_agent(
            "code_reviewer",
            model_alias,
            **reviewer_config
        )
        agents.append(code_reviewer)
    except Exception as e:
        print(f"Warning: Could not create code_reviewer agent: {e}")
    
    # 如果没有可用的agents，创建模拟agents
    if not agents and not AUTOGEN_AVAILABLE:
        class MockAgent:
            def __init__(self, name):
                self.name = name
        
        agents = [
            MockAgent("Developer"),
            MockAgent("CodeReviewer")
        ]
    
    if not agents:
        raise ValueError("No agents available for the simple development team")
    
    # 创建终止条件
    max_turns = kwargs.get('max_turns', 6)
    termination_condition = MaxMessageTermination(max_turns)

    # 创建RoundRobinGroupChat团队
    team = RoundRobinGroupChat(
        participants=agents,
        termination_condition=termination_condition,
        max_turns=max_turns,
        **kwargs.get('team_config', {})
    )

    return team
